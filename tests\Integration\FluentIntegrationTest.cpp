// tests/Integration/FluentIntegrationTest.cpp
#include <QtTest/QtTest>
#include <QtTest/QSignalSpy>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QApplication>

#include "FluentQt/Components/FluentButton.h"
#include "FluentQt/Components/FluentCard.h"
#include "FluentQt/Components/FluentProgressBar.h"
#include "FluentQt/Components/FluentTooltip.h"
#include "FluentQt/Components/FluentBadge.h"
#include "FluentQt/Components/FluentTextInput.h"
#include "FluentQt/Components/FluentCheckBox.h"
#include "FluentQt/Components/FluentRadioButton.h"
#include "FluentQt/Styling/FluentTheme.h"

using namespace FluentQt::Components;
using namespace FluentQt::Core;

class FluentIntegrationTest : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // System Integration Tests
    void testSystemInitialization();
    void testCrossSystemCommunication();
    void testSystemShutdown();

    // Component Integration Tests
    void testComponentInteraction();
    void testComponentThemeIntegration();
    void testComponentAccessibilityIntegration();
    void testComponentResponsiveIntegration();

    // Performance Integration Tests
    void testPerformanceMonitoringIntegration();
    void testMemoryManagementIntegration();
    void testRenderOptimizationIntegration();

    // Theme Integration Tests
    void testThemeSystemIntegration();
    void testThemeTransitionIntegration();
    void testCustomThemeIntegration();

    // End-to-End Workflow Tests
    void testCompleteUserWorkflow();
    void testComplexFormWorkflow();
    void testNavigationWorkflow();

private:
    void createTestApplication();
    void setupTestComponents();
    void verifySystemStates();

private:
    QWidget* m_testWidget;
    FluentButton* m_testButton;
    FluentTextInput* m_testTextInput;
    FluentCard* m_testCard;

    // Helper methods
    void createTestApplication();
    void setupTestComponents();
    void verifySystemStates();

    // Component interaction tests
    void testButtonCardInteraction();
    void testFormComponentsInteraction();
    void testProgressBarIntegration();
    void testTooltipIntegration();
    void testBadgeIntegration();

    // Layout integration tests
    void testComplexLayout();
    void testNestedComponents();
    void testDynamicComponentAddition();

    // Theme integration tests
    void testThemeChangeAcrossComponents();
    void testThemeConsistency();

    // State synchronization tests
    void testStateSynchronization();
    void testCascadingStateChanges();

    // Event propagation tests
    void testEventPropagation();
    void testFocusChain();

    // Performance tests
    void testManyComponentsPerformance();
    void testRapidStateChanges();

    // Memory management tests
    void testComponentLifecycle();
    void testParentChildRelationships();

private:
    QWidget* m_container;
    FluentButton* m_button;
    FluentCard* m_card;
    FluentProgressBar* m_progressBar;
    FluentTooltip* m_tooltip;
    FluentBadge* m_badge;
};

void FluentIntegrationTest::initTestCase() {
    // Initialize test environment
}

void FluentIntegrationTest::cleanupTestCase() {
    // Clean up test environment
}

void FluentIntegrationTest::init() {
    // Create container and components for each test
    m_container = new QWidget();
    m_container->resize(800, 600);
    m_container->show();
    
    m_button = new FluentButton("Test Button", m_container);
    m_card = new FluentCard("Test Card", m_container);
    m_progressBar = new FluentProgressBar(m_container);
    m_tooltip = new FluentTooltip();
    m_badge = new FluentBadge();
}

void FluentIntegrationTest::cleanup() {
    // Clean up after each test
    delete m_tooltip;
    delete m_badge;
    delete m_container; // This will delete child components
    
    m_container = nullptr;
    m_button = nullptr;
    m_card = nullptr;
    m_progressBar = nullptr;
    m_tooltip = nullptr;
    m_badge = nullptr;
}

void FluentIntegrationTest::testButtonCardInteraction() {
    // Test interaction between button and card components
    QVBoxLayout* layout = new QVBoxLayout(m_container);
    layout->addWidget(m_card);
    
    // Add button to card content
    m_card->setContentWidget(m_button);
    
    // Test that button click affects card state
    QSignalSpy buttonClickedSpy(m_button, &FluentButton::clicked);
    QSignalSpy cardClickedSpy(m_card, &FluentCard::cardClicked);
    
    // Click button
    m_button->animateClick();
    QCOMPARE(buttonClickedSpy.count(), 1);
    
    // Test that card can contain button without issues
    QCOMPARE(m_card->contentWidget(), m_button);
    QCOMPARE(m_button->parent(), m_card);
}

void FluentIntegrationTest::testFormComponentsInteraction() {
    // Test interaction between form components
    FluentTextInput* textInput = new FluentTextInput(m_container);
    FluentCheckBox* checkBox = new FluentCheckBox("Enable feature", m_container);
    FluentRadioButton* radio1 = new FluentRadioButton("Option 1", m_container);
    FluentRadioButton* radio2 = new FluentRadioButton("Option 2", m_container);
    
    QVBoxLayout* layout = new QVBoxLayout(m_container);
    layout->addWidget(textInput);
    layout->addWidget(checkBox);
    layout->addWidget(radio1);
    layout->addWidget(radio2);
    
    // Test form state coordination
    QSignalSpy textChangedSpy(textInput, &FluentTextInput::textChanged);
    QSignalSpy checkBoxToggledSpy(checkBox, &FluentCheckBox::toggled);
    QSignalSpy radio1ToggledSpy(radio1, &FluentRadioButton::toggled);
    QSignalSpy radio2ToggledSpy(radio2, &FluentRadioButton::toggled);
    
    // Simulate form interaction
    textInput->setText("Test input");
    QCOMPARE(textChangedSpy.count(), 1);
    
    checkBox->setChecked(true);
    QCOMPARE(checkBoxToggledSpy.count(), 1);
    
    radio1->setChecked(true);
    QCOMPARE(radio1ToggledSpy.count(), 1);
    QVERIFY(radio1->isChecked());
    
    // Test radio button exclusivity
    radio2->setChecked(true);
    QCOMPARE(radio2ToggledSpy.count(), 1);
    QVERIFY(radio2->isChecked());
    QVERIFY(!radio1->isChecked()); // Should be unchecked due to exclusivity
    
    // Clean up
    delete textInput;
    delete checkBox;
    delete radio1;
    delete radio2;
}

void FluentIntegrationTest::testProgressBarIntegration() {
    // Test progress bar integration with other components
    QVBoxLayout* layout = new QVBoxLayout(m_container);
    layout->addWidget(m_progressBar);
    layout->addWidget(m_button);
    
    // Connect button to progress bar
    QSignalSpy buttonClickedSpy(m_button, &FluentButton::clicked);
    QSignalSpy progressValueChangedSpy(m_progressBar, &FluentProgressBar::valueChanged);
    
    // Test that button can control progress bar
    connect(m_button, &FluentButton::clicked, [this]() {
        int currentValue = m_progressBar->value();
        m_progressBar->setValue(currentValue + 10);
    });
    
    // Click button multiple times
    m_button->animateClick();
    QCOMPARE(buttonClickedSpy.count(), 1);
    QCOMPARE(progressValueChangedSpy.count(), 1);
    QCOMPARE(m_progressBar->value(), 10);

    m_button->animateClick();
    QCOMPARE(buttonClickedSpy.count(), 2);
    QCOMPARE(progressValueChangedSpy.count(), 2);
    QCOMPARE(m_progressBar->value(), 20);
}

void FluentIntegrationTest::testTooltipIntegration() {
    // Test tooltip integration with components
    QVBoxLayout* layout = new QVBoxLayout(m_container);
    layout->addWidget(m_button);
    
    // Set tooltip for button
    m_tooltip->setText("This is a test button");
    m_tooltip->showFor(m_button);
    
    // Test that tooltip can be shown and hidden
    QCOMPARE(m_tooltip->text(), QString("This is a test button"));
    
    // Test tooltip positioning
    m_tooltip->setPosition(FluentTooltipPosition::Top);
    m_tooltip->showFor(m_button, FluentTooltipPosition::Bottom);
    
    // Tooltip should work without crashing
    QVERIFY(true);
}

void FluentIntegrationTest::testBadgeIntegration() {
    // Test badge integration with components
    QVBoxLayout* layout = new QVBoxLayout(m_container);
    layout->addWidget(m_button);
    
    // Attach badge to button
    m_badge->setCount(5);
    m_badge->attachTo(m_button);
    
    QCOMPARE(m_badge->attachedWidget(), m_button);
    QCOMPARE(m_badge->count(), 5);
    QVERIFY(!m_badge->isEmpty());
    
    // Test badge position
    m_badge->setPosition(FluentBadgePosition::TopRight);
    QCOMPARE(m_badge->position(), FluentBadgePosition::TopRight);
    
    // Test badge updates
    QSignalSpy badgeCountChangedSpy(m_badge, &FluentBadge::countChanged);
    m_badge->increment();
    QCOMPARE(m_badge->count(), 6);
    QCOMPARE(badgeCountChangedSpy.count(), 1);
}

void FluentIntegrationTest::testComplexLayout() {
    // Test complex layout with multiple component types
    QVBoxLayout* mainLayout = new QVBoxLayout(m_container);
    
    // Create header with title and button
    QHBoxLayout* headerLayout = new QHBoxLayout();
    FluentButton* headerButton = new FluentButton("Action", m_container);
    auto* searchInput = new FluentTextInput(m_container);
    searchInput->setPlaceholderText("Search...");
    headerLayout->addWidget(searchInput);
    headerLayout->addStretch();
    headerLayout->addWidget(headerButton);
    
    // Create content area with cards
    QHBoxLayout* contentLayout = new QHBoxLayout();
    FluentCard* card1 = new FluentCard("Card 1", m_container);
    FluentCard* card2 = new FluentCard("Card 2", m_container);
    contentLayout->addWidget(card1);
    contentLayout->addWidget(card2);
    
    // Create footer with progress bar
    QHBoxLayout* footerLayout = new QHBoxLayout();
    footerLayout->addWidget(m_progressBar);
    
    // Assemble main layout
    mainLayout->addLayout(headerLayout);
    mainLayout->addLayout(contentLayout);
    mainLayout->addLayout(footerLayout);
    
    // Test that all components are properly laid out
    QVERIFY(m_container->layout() != nullptr);
    QCOMPARE(mainLayout->count(), 3);
    
    // Test component interactions in complex layout
    QSignalSpy headerButtonClickedSpy(headerButton, &FluentButton::clicked);
    QTest::mouseClick(headerButton, Qt::LeftButton);
    QCOMPARE(headerButtonClickedSpy.count(), 1);
    
    // Clean up will be handled by container deletion
}

void FluentIntegrationTest::testNestedComponents() {
    // Test nested component structures
    QVBoxLayout* layout = new QVBoxLayout(m_container);
    layout->addWidget(m_card);
    
    // Create nested structure: Card -> Button -> Badge
    m_card->setContentWidget(m_button);
    m_badge->attachTo(m_button);
    
    // Test that nested structure works correctly
    QCOMPARE(m_card->contentWidget(), m_button);
    QCOMPARE(m_badge->attachedWidget(), m_button);
    QCOMPARE(m_button->parent(), m_card);
    
    // Test interactions in nested structure
    QSignalSpy buttonClickedSpy(m_button, &FluentButton::clicked);
    QSignalSpy cardClickedSpy(m_card, &FluentCard::cardClicked);
    
    QTest::mouseClick(m_button, Qt::LeftButton);
    QCOMPARE(buttonClickedSpy.count(), 1);
    
    // Test that badge updates work in nested structure
    QSignalSpy badgeCountChangedSpy(m_badge, &FluentBadge::countChanged);
    m_badge->setCount(10);
    QCOMPARE(m_badge->count(), 10);
    QCOMPARE(badgeCountChangedSpy.count(), 1);
}

void FluentIntegrationTest::testDynamicComponentAddition() {
    // Test dynamic addition and removal of components
    QVBoxLayout* layout = new QVBoxLayout(m_container);

    // Start with empty layout
    QCOMPARE(layout->count(), 0);

    // Dynamically add components
    FluentButton* dynamicButton = new FluentButton("Dynamic Button");
    layout->addWidget(dynamicButton);
    QCOMPARE(layout->count(), 1);

    FluentCard* dynamicCard = new FluentCard("Dynamic Card");
    layout->addWidget(dynamicCard);
    QCOMPARE(layout->count(), 2);

    // Test that dynamically added components work correctly
    QSignalSpy buttonClickedSpy(dynamicButton, &FluentButton::clicked);
    dynamicButton->animateClick();
    QCOMPARE(buttonClickedSpy.count(), 1);

    // Test dynamic removal
    layout->removeWidget(dynamicButton);
    delete dynamicButton;
    QCOMPARE(layout->count(), 1);

    // Remaining component should still work
    QSignalSpy cardClickedSpy(dynamicCard, &FluentCard::cardClicked);
    // Card click would need to be simulated through mouse events
    // For now, just verify the card exists
    QVERIFY(dynamicCard != nullptr);

    // Clean up
    delete dynamicCard;
}

void FluentIntegrationTest::testThemeChangeAcrossComponents() {
    // Test theme changes across multiple components
    QVBoxLayout* layout = new QVBoxLayout(m_container);
    layout->addWidget(m_button);
    layout->addWidget(m_card);
    layout->addWidget(m_progressBar);

    auto& theme = FluentQt::Styling::FluentTheme::instance();
    auto originalMode = theme.mode();

    // Change theme mode
    auto newMode = (originalMode == FluentQt::Styling::FluentThemeMode::Light) ?
                   FluentQt::Styling::FluentThemeMode::Dark :
                   FluentQt::Styling::FluentThemeMode::Light;
    theme.setMode(newMode);

    // All components should respond to theme change
    // We can't easily test visual changes, but we can verify functionality
    QSignalSpy buttonClickedSpy(m_button, &FluentButton::clicked);
    QSignalSpy progressValueChangedSpy(m_progressBar, &FluentProgressBar::valueChanged);

    m_button->animateClick();
    QCOMPARE(buttonClickedSpy.count(), 1);

    m_progressBar->setValue(50);
    QCOMPARE(progressValueChangedSpy.count(), 1);
    QCOMPARE(m_progressBar->value(), 50);

    // Restore original theme
    theme.setMode(originalMode);
}

void FluentIntegrationTest::testThemeConsistency() {
    // Test that all components use consistent theming
    QVBoxLayout* layout = new QVBoxLayout(m_container);

    // Create multiple components of different types
    FluentButton* button1 = new FluentButton("Button 1", m_container);
    FluentButton* button2 = new FluentButton("Button 2", m_container);
    FluentCard* card1 = new FluentCard("Card 1", m_container);
    FluentCard* card2 = new FluentCard("Card 2", m_container);

    layout->addWidget(button1);
    layout->addWidget(button2);
    layout->addWidget(card1);
    layout->addWidget(card2);

    // All components should be using the same theme
    auto& theme = FluentQt::Styling::FluentTheme::instance();
    auto currentMode = theme.mode();

    // Change theme and verify all components still function
    auto newMode = (currentMode == FluentQt::Styling::FluentThemeMode::Light) ?
                   FluentQt::Styling::FluentThemeMode::Dark :
                   FluentQt::Styling::FluentThemeMode::Light;
    theme.setMode(newMode);

    // Test that all components still work after theme change
    QSignalSpy button1ClickedSpy(button1, &FluentButton::clicked);
    QSignalSpy button2ClickedSpy(button2, &FluentButton::clicked);

    button1->animateClick();
    button2->animateClick();

    QCOMPARE(button1ClickedSpy.count(), 1);
    QCOMPARE(button2ClickedSpy.count(), 1);

    // Restore original theme
    theme.setMode(currentMode);

    // Clean up
    delete button1;
    delete button2;
    delete card1;
    delete card2;
}

void FluentIntegrationTest::testStateSynchronization() {
    // Test state synchronization between related components
    QVBoxLayout* layout = new QVBoxLayout(m_container);
    layout->addWidget(m_button);
    layout->addWidget(m_progressBar);

    // Connect button state to progress bar
    connect(m_button, &FluentButton::clicked, [this]() {
        if (m_progressBar->value() < m_progressBar->maximum()) {
            m_progressBar->setValue(m_progressBar->value() + 10);
        } else {
            m_progressBar->setValue(0);
        }
    });

    // Test synchronized state changes
    QSignalSpy progressValueChangedSpy(m_progressBar, &FluentProgressBar::valueChanged);

    QCOMPARE(m_progressBar->value(), 0);

    QTest::mouseClick(m_button, Qt::LeftButton);
    QCOMPARE(m_progressBar->value(), 10);
    QCOMPARE(progressValueChangedSpy.count(), 1);

    QTest::mouseClick(m_button, Qt::LeftButton);
    QCOMPARE(m_progressBar->value(), 20);
    QCOMPARE(progressValueChangedSpy.count(), 2);
}

void FluentIntegrationTest::testCascadingStateChanges() {
    // Test cascading state changes across multiple components
    FluentCheckBox* checkBox = new FluentCheckBox("Enable features", m_container);
    FluentButton* button1 = new FluentButton("Feature 1", m_container);
    FluentButton* button2 = new FluentButton("Feature 2", m_container);

    QVBoxLayout* layout = new QVBoxLayout(m_container);
    layout->addWidget(checkBox);
    layout->addWidget(button1);
    layout->addWidget(button2);

    // Connect checkbox to enable/disable buttons
    connect(checkBox, &FluentCheckBox::toggled, [button1, button2](bool checked) {
        button1->setEnabled(checked);
        button2->setEnabled(checked);
    });

    // Test cascading state changes
    QVERIFY(button1->isEnabled());
    QVERIFY(button2->isEnabled());

    checkBox->setChecked(false);
    QVERIFY(!button1->isEnabled());
    QVERIFY(!button2->isEnabled());

    checkBox->setChecked(true);
    QVERIFY(button1->isEnabled());
    QVERIFY(button2->isEnabled());

    // Clean up
    delete checkBox;
    delete button1;
    delete button2;
}

void FluentIntegrationTest::testEventPropagation() {
    // Test event propagation through component hierarchy
    QVBoxLayout* layout = new QVBoxLayout(m_container);
    layout->addWidget(m_card);

    // Add button to card
    m_card->setContentWidget(m_button);

    // Test that events propagate correctly
    QSignalSpy buttonClickedSpy(m_button, &FluentButton::clicked);
    QSignalSpy cardClickedSpy(m_card, &FluentCard::cardClicked);

    // Click button - should trigger button clicked but not card clicked
    m_button->animateClick();
    QCOMPARE(buttonClickedSpy.count(), 1);
    // Card clicked depends on implementation - button click might not propagate to card

    // Test that components can handle events independently
    QVERIFY(true);
}

void FluentIntegrationTest::testFocusChain() {
    // Test focus chain across multiple components
    FluentTextInput* textInput = new FluentTextInput(m_container);
    FluentCheckBox* checkBox = new FluentCheckBox("Check", m_container);

    QVBoxLayout* layout = new QVBoxLayout(m_container);
    layout->addWidget(textInput);
    layout->addWidget(m_button);
    layout->addWidget(checkBox);

    // Test focus chain
    textInput->setFocus();
    QVERIFY(textInput->hasFocus());

    // Simulate Tab key to move focus
    QKeyEvent tabEvent(QEvent::KeyPress, Qt::Key_Tab, Qt::NoModifier);
    QApplication::sendEvent(textInput, &tabEvent);

    // Focus should move to next component in chain
    // The exact behavior depends on focus policy and tab order
    QVERIFY(true);

    // Clean up
    delete textInput;
    delete checkBox;
}

void FluentIntegrationTest::testManyComponentsPerformance() {
    // Test performance with many components
    QVBoxLayout* layout = new QVBoxLayout(m_container);

    const int componentCount = 100;
    QList<FluentButton*> buttons;

    // Create many components
    for (int i = 0; i < componentCount; ++i) {
        FluentButton* button = new FluentButton(QString("Button %1").arg(i), m_container);
        buttons.append(button);
        layout->addWidget(button);
    }

    QCOMPARE(buttons.size(), componentCount);
    QCOMPARE(layout->count(), componentCount);

    // Test that all components work correctly
    QSignalSpy firstButtonClickedSpy(buttons.first(), &FluentButton::clicked);
    QSignalSpy lastButtonClickedSpy(buttons.last(), &FluentButton::clicked);

    buttons.first()->animateClick();
    buttons.last()->animateClick();

    QCOMPARE(firstButtonClickedSpy.count(), 1);
    QCOMPARE(lastButtonClickedSpy.count(), 1);

    // Clean up will be handled by container deletion
}

void FluentIntegrationTest::testRapidStateChanges() {
    // Test rapid state changes across components
    QVBoxLayout* layout = new QVBoxLayout(m_container);
    layout->addWidget(m_button);
    layout->addWidget(m_progressBar);

    QSignalSpy buttonClickedSpy(m_button, &FluentButton::clicked);
    QSignalSpy progressValueChangedSpy(m_progressBar, &FluentProgressBar::valueChanged);

    // Perform rapid state changes
    for (int i = 0; i < 10; ++i) {
        m_button->animateClick();
        m_progressBar->setValue(i * 10);
    }

    QCOMPARE(buttonClickedSpy.count(), 10);
    QCOMPARE(progressValueChangedSpy.count(), 10);
    QCOMPARE(m_progressBar->value(), 90);
}

void FluentIntegrationTest::testComponentLifecycle() {
    // Test component lifecycle management
    QVBoxLayout* layout = new QVBoxLayout(m_container);

    // Create components with proper parent-child relationships
    FluentCard* parentCard = new FluentCard("Parent Card", m_container);
    FluentButton* childButton = new FluentButton("Child Button", parentCard);

    layout->addWidget(parentCard);
    parentCard->setContentWidget(childButton);

    // Test that parent-child relationships are correct
    QCOMPARE(childButton->parent(), parentCard);
    QCOMPARE(parentCard->contentWidget(), childButton);

    // Test that deleting parent cleans up children
    delete parentCard; // This should also delete childButton

    // Create new components to verify no memory issues
    FluentButton* newButton = new FluentButton("New Button", m_container);
    layout->addWidget(newButton);

    QSignalSpy newButtonClickedSpy(newButton, &FluentButton::clicked);
    newButton->animateClick();
    QCOMPARE(newButtonClickedSpy.count(), 1);

    // Clean up
    delete newButton;
}

void FluentIntegrationTest::testParentChildRelationships() {
    // Test complex parent-child relationships
    QVBoxLayout* layout = new QVBoxLayout(m_container);

    // Create hierarchy: Container -> Card -> Button -> Badge
    layout->addWidget(m_card);
    m_card->setContentWidget(m_button);
    m_badge->attachTo(m_button);

    // Test relationships
    QCOMPARE(m_card->parent(), m_container);
    QCOMPARE(m_button->parent(), m_card);
    QCOMPARE(m_badge->attachedWidget(), m_button);

    // Test that all components in hierarchy work
    QSignalSpy buttonClickedSpy(m_button, &FluentButton::clicked);
    QSignalSpy badgeCountChangedSpy(m_badge, &FluentBadge::countChanged);

    m_button->animateClick();
    m_badge->increment();

    QCOMPARE(buttonClickedSpy.count(), 1);
    QCOMPARE(badgeCountChangedSpy.count(), 1);

    // Test that hierarchy is maintained after operations
    QCOMPARE(m_card->contentWidget(), m_button);
    QCOMPARE(m_badge->attachedWidget(), m_button);
}

QTEST_MAIN(FluentIntegrationTest)
#include "FluentIntegrationTest.moc"
