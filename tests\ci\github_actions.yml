# GitHub Actions workflow for FluentQt tests
# This file should be placed in .github/workflows/ in the repository root

name: FluentQt Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  BUILD_TYPE: Release
  QT_VERSION: 6.5.0

jobs:
  test:
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        qt-version: [6.5.0, 6.6.0]
        build-type: [Debug, Release]
        
    runs-on: ${{ matrix.os }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        submodules: recursive
    
    - name: Install Qt
      uses: jurplel/install-qt-action@v3
      with:
        version: ${{ matrix.qt-version }}
        modules: 'qtbase qttools'
        cache: true
    
    - name: Install dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          build-essential \
          cmake \
          ninja-build \
          lcov \
          valgrind \
          xvfb
    
    - name: Install dependencies (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        brew install cmake ninja lcov
    
    - name: Install dependencies (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        choco install cmake ninja
    
    - name: Configure CMake
      run: |
        cmake -B build \
          -G Ninja \
          -DCMAKE_BUILD_TYPE=${{ matrix.build-type }} \
          -DCMAKE_PREFIX_PATH=${{ env.Qt6_DIR }} \
          -DFLUENT_QT_BUILD_TESTS=ON \
          -DFLUENT_QT_ENABLE_COVERAGE=ON
    
    - name: Build
      run: cmake --build build --config ${{ matrix.build-type }}
    
    - name: Run tests (Linux)
      if: matrix.os == 'ubuntu-latest'
      run: |
        cd build
        xvfb-run -a ctest --output-on-failure --parallel 4
    
    - name: Run tests (Windows/macOS)
      if: matrix.os != 'ubuntu-latest'
      run: |
        cd build
        ctest --output-on-failure --parallel 4
    
    - name: Generate coverage report (Ubuntu Debug only)
      if: matrix.os == 'ubuntu-latest' && matrix.build-type == 'Debug'
      run: |
        cd build
        make coverage
    
    - name: Upload coverage to Codecov
      if: matrix.os == 'ubuntu-latest' && matrix.build-type == 'Debug'
      uses: codecov/codecov-action@v3
      with:
        file: build/coverage/coverage_filtered.info
        flags: unittests
        name: codecov-umbrella
    
    - name: Run memory checks (Ubuntu Debug only)
      if: matrix.os == 'ubuntu-latest' && matrix.build-type == 'Debug'
      run: |
        cd build
        python3 ../tests/run_tests.py --memcheck FluentButtonTest
    
    - name: Upload test artifacts
      if: failure()
      uses: actions/upload-artifact@v3
      with:
        name: test-results-${{ matrix.os }}-${{ matrix.qt-version }}-${{ matrix.build-type }}
        path: |
          build/test_report.json
          build/Testing/
          build/coverage/
          build/valgrind_*.xml

  benchmark:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Install Qt
      uses: jurplel/install-qt-action@v3
      with:
        version: ${{ env.QT_VERSION }}
        modules: 'qtbase qttools'
        cache: true
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential cmake ninja-build xvfb
    
    - name: Configure CMake
      run: |
        cmake -B build \
          -G Ninja \
          -DCMAKE_BUILD_TYPE=Release \
          -DFLUENT_QT_BUILD_TESTS=ON \
          -DFLUENT_QT_BUILD_BENCHMARKS=ON
    
    - name: Build
      run: cmake --build build --config Release
    
    - name: Run benchmarks
      run: |
        cd build
        xvfb-run -a python3 ../tests/run_tests.py --benchmark
    
    - name: Upload benchmark results
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-results
        path: build/benchmark_*.json

  accessibility:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Install Qt
      uses: jurplel/install-qt-action@v3
      with:
        version: ${{ env.QT_VERSION }}
        modules: 'qtbase qttools'
        cache: true
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          build-essential \
          cmake \
          ninja-build \
          xvfb \
          at-spi2-core \
          libatspi2.0-dev
    
    - name: Configure CMake
      run: |
        cmake -B build \
          -G Ninja \
          -DCMAKE_BUILD_TYPE=Debug \
          -DFLUENT_QT_BUILD_TESTS=ON \
          -DFLUENT_QT_ENABLE_ACCESSIBILITY=ON
    
    - name: Build
      run: cmake --build build --config Debug
    
    - name: Run accessibility tests
      run: |
        cd build
        xvfb-run -a python3 ../tests/run_tests.py --pattern "*AccessibilityTest"
    
    - name: Upload accessibility test results
      uses: actions/upload-artifact@v3
      with:
        name: accessibility-test-results
        path: build/test_report.json
