// tests/FluentCarouselVariantsTest.cpp
#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QWidget>
#include <QLabel>
#include <QPixmap>
#include <QTimer>

#include "FluentQt/Components/FluentBasicCarousel.h"
#include "FluentQt/Components/FluentAutoCarousel.h"
#include "FluentQt/Components/FluentIndicatorCarousel.h"
#include "FluentQt/Components/FluentTouchCarousel.h"

using namespace FluentQt::Components;

class FluentCarouselVariantsTest : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // FluentBasicCarousel tests
    void testBasicCarouselButtons();
    void testBasicCarouselKeyboard();
    void testBasicCarouselAccessibility();

    // FluentAutoCarousel tests
    void testAutoCarouselConstructor();
    void testAutoCarouselAutoPlay();
    void testAutoCarouselControls();
    void testAutoCarouselProgress();
    void testAutoCarouselPauseResume();
    void testAutoCarouselHoverBehavior();

    // FluentIndicatorCarousel tests
    void testIndicatorCarouselConstructor();
    void testIndicatorCarouselDots();
    void testIndicatorCarouselLines();
    void testIndicatorCarouselNumbers();
    void testIndicatorCarouselThumbnails();
    void testIndicatorCarouselInteraction();
    void testIndicatorCarouselPositioning();

    // FluentTouchCarousel tests
    void testTouchCarouselConstructor();
    void testTouchCarouselConfiguration();
    void testTouchCarouselGestures();
    void testTouchCarouselMomentum();
    void testTouchCarouselEdgeBehavior();

    // Integration tests
    void testCarouselCombinations();
    void testCarouselPerformance();
    void testCarouselMemoryManagement();

private:
    FluentBasicCarousel* m_basicCarousel{nullptr};
    FluentAutoCarousel* m_autoCarousel{nullptr};
    FluentIndicatorCarousel* m_indicatorCarousel{nullptr};
    FluentTouchCarousel* m_touchCarousel{nullptr};

    // Helper methods
    void addTestItems(FluentCarousel* carousel, int count = 5);
    QWidget* createTestWidget(const QString& text, const QColor& color = Qt::blue);
    QPixmap createTestPixmap(const QSize& size, const QColor& color);
};

void FluentCarouselVariantsTest::initTestCase() {
    qApp->setApplicationName("FluentCarouselVariantsTest");
}

void FluentCarouselVariantsTest::cleanupTestCase() {
    // Global cleanup
}

void FluentCarouselVariantsTest::init() {
    // Create test instances
    m_basicCarousel = new FluentBasicCarousel();
    m_autoCarousel = new FluentAutoCarousel();
    m_indicatorCarousel = new FluentIndicatorCarousel();
    m_touchCarousel = new FluentTouchCarousel();

    // Show widgets for proper testing
    m_basicCarousel->show();
    m_autoCarousel->show();
    m_indicatorCarousel->show();
    m_touchCarousel->show();

    // Wait for widgets to be exposed
    QTest::qWaitForWindowExposed(m_basicCarousel);
    QTest::qWaitForWindowExposed(m_autoCarousel);
    QTest::qWaitForWindowExposed(m_indicatorCarousel);
    QTest::qWaitForWindowExposed(m_touchCarousel);
}

void FluentCarouselVariantsTest::cleanup() {
    delete m_basicCarousel;
    delete m_autoCarousel;
    delete m_indicatorCarousel;
    delete m_touchCarousel;

    m_basicCarousel = nullptr;
    m_autoCarousel = nullptr;
    m_indicatorCarousel = nullptr;
    m_touchCarousel = nullptr;
}

// FluentBasicCarousel tests
void FluentCarouselVariantsTest::testBasicCarouselButtons() {
    addTestItems(m_basicCarousel, 3);

    // Test navigation button visibility
    QVERIFY(m_basicCarousel->showNavigationButtons());
    
    m_basicCarousel->setShowNavigationButtons(false);
    QVERIFY(!m_basicCarousel->showNavigationButtons());
    
    m_basicCarousel->setShowNavigationButtons(true);
    QVERIFY(m_basicCarousel->showNavigationButtons());

    // Test button functionality through signals
    QSignalSpy nextClickedSpy(m_basicCarousel, &FluentBasicCarousel::nextButtonClicked);
    QSignalSpy previousClickedSpy(m_basicCarousel, &FluentBasicCarousel::previousButtonClicked);

    // Simulate button clicks (would need access to internal buttons)
    // For now, test the navigation methods that buttons would call
    m_basicCarousel->next();
    QCOMPARE(m_basicCarousel->currentIndex(), 1);

    m_basicCarousel->previous();
    QCOMPARE(m_basicCarousel->currentIndex(), 0);
}

void FluentCarouselVariantsTest::testBasicCarouselKeyboard() {
    addTestItems(m_basicCarousel, 3);
    m_basicCarousel->setFocus();

    // Test keyboard navigation
    QKeyEvent rightKey(QEvent::KeyPress, Qt::Key_Right, Qt::NoModifier);
    QApplication::sendEvent(m_basicCarousel, &rightKey);
    QCOMPARE(m_basicCarousel->currentIndex(), 1);

    QKeyEvent leftKey(QEvent::KeyPress, Qt::Key_Left, Qt::NoModifier);
    QApplication::sendEvent(m_basicCarousel, &leftKey);
    QCOMPARE(m_basicCarousel->currentIndex(), 0);

    // Test space/enter for activation
    QKeyEvent spaceKey(QEvent::KeyPress, Qt::Key_Space, Qt::NoModifier);
    QApplication::sendEvent(m_basicCarousel, &spaceKey);
    // Space might trigger some action depending on implementation
}

void FluentCarouselVariantsTest::testBasicCarouselAccessibility() {
    addTestItems(m_basicCarousel, 3);

    // Test accessibility properties
    QVERIFY(!m_basicCarousel->accessibleName().isEmpty());
    
    // Test navigation button accessibility
    // Would need access to internal navigation buttons to test their accessibility
    
    // Test keyboard focus handling
    m_basicCarousel->setFocus();
    QVERIFY(m_basicCarousel->hasFocus());
}

// FluentAutoCarousel tests
void FluentCarouselVariantsTest::testAutoCarouselConstructor() {
    // Test default constructor
    auto* autoCarousel1 = new FluentAutoCarousel();
    QVERIFY(autoCarousel1 != nullptr);
    QVERIFY(autoCarousel1->isAutoPlayEnabled());
    QCOMPARE(autoCarousel1->autoPlayInterval(), 3000); // Default interval
    delete autoCarousel1;

    // Test constructor with configuration
    FluentCarouselConfig config;
    config.autoPlay = FluentCarouselAutoPlay::Disabled;
    config.autoPlayInterval = 2000;
    
    auto* autoCarousel2 = new FluentAutoCarousel(config);
    QVERIFY(!autoCarousel2->isAutoPlayEnabled());
    QCOMPARE(autoCarousel2->autoPlayInterval(), 2000);
    delete autoCarousel2;
}

void FluentCarouselVariantsTest::testAutoCarouselAutoPlay() {
    addTestItems(m_autoCarousel, 3);

    // Test auto-play functionality
    QVERIFY(m_autoCarousel->isAutoPlayEnabled());
    
    // Set shorter interval for testing
    m_autoCarousel->setAutoPlayInterval(500);
    QCOMPARE(m_autoCarousel->autoPlayInterval(), 500);

    // Start auto-play
    m_autoCarousel->startAutoPlay();
    QVERIFY(m_autoCarousel->isAutoPlayActive());

    // Wait for auto progression
    QSignalSpy currentIndexChangedSpy(m_autoCarousel, &FluentAutoCarousel::currentIndexChanged);
    QTest::qWait(600); // Wait longer than interval
    
    // Should have progressed to next item
    QVERIFY(currentIndexChangedSpy.count() > 0);
    QCOMPARE(m_autoCarousel->currentIndex(), 1);

    // Stop auto-play
    m_autoCarousel->stopAutoPlay();
    QVERIFY(!m_autoCarousel->isAutoPlayActive());
}

void FluentCarouselVariantsTest::testAutoCarouselControls() {
    addTestItems(m_autoCarousel, 3);

    // Test play/pause controls
    QVERIFY(m_autoCarousel->showPlayPauseButton());
    
    m_autoCarousel->setShowPlayPauseButton(false);
    QVERIFY(!m_autoCarousel->showPlayPauseButton());
    
    m_autoCarousel->setShowPlayPauseButton(true);
    QVERIFY(m_autoCarousel->showPlayPauseButton());

    // Test control signals
    QSignalSpy playClickedSpy(m_autoCarousel, &FluentAutoCarousel::playButtonClicked);
    QSignalSpy pauseClickedSpy(m_autoCarousel, &FluentAutoCarousel::pauseButtonClicked);

    // Test programmatic play/pause
    m_autoCarousel->pauseAutoPlay();
    QVERIFY(!m_autoCarousel->isAutoPlayActive());

    m_autoCarousel->resumeAutoPlay();
    QVERIFY(m_autoCarousel->isAutoPlayActive());
}

void FluentCarouselVariantsTest::testAutoCarouselProgress() {
    addTestItems(m_autoCarousel, 3);

    // Test progress indicator
    QVERIFY(m_autoCarousel->showProgressIndicator());
    
    m_autoCarousel->setShowProgressIndicator(false);
    QVERIFY(!m_autoCarousel->showProgressIndicator());
    
    m_autoCarousel->setShowProgressIndicator(true);
    QVERIFY(m_autoCarousel->showProgressIndicator());

    // Test progress updates
    QSignalSpy progressSpy(m_autoCarousel, &FluentAutoCarousel::autoPlayProgressChanged);
    
    m_autoCarousel->setAutoPlayInterval(1000);
    m_autoCarousel->startAutoPlay();
    
    // Wait for progress updates
    QTest::qWait(300);
    QVERIFY(progressSpy.count() > 0);
    
    // Progress should be between 0 and 1
    qreal progress = progressSpy.last().first().toReal();
    QVERIFY(progress >= 0.0 && progress <= 1.0);
}

void FluentCarouselVariantsTest::testAutoCarouselPauseResume() {
    addTestItems(m_autoCarousel, 3);

    // Test pause on hover
    m_autoCarousel->setPauseOnHover(true);
    QVERIFY(m_autoCarousel->pauseOnHover());

    m_autoCarousel->startAutoPlay();
    QVERIFY(m_autoCarousel->isAutoPlayActive());

    // Simulate hover enter
    QEvent hoverEnter(QEvent::HoverEnter);
    QApplication::sendEvent(m_autoCarousel, &hoverEnter);
    
    // Auto-play should pause on hover
    QVERIFY(!m_autoCarousel->isAutoPlayActive());

    // Simulate hover leave
    QEvent hoverLeave(QEvent::HoverLeave);
    QApplication::sendEvent(m_autoCarousel, &hoverLeave);
    
    // Auto-play should resume
    QVERIFY(m_autoCarousel->isAutoPlayActive());

    m_autoCarousel->stopAutoPlay();
}

void FluentCarouselVariantsTest::testAutoCarouselHoverBehavior() {
    addTestItems(m_autoCarousel, 3);

    // Test pause on hover setting
    m_autoCarousel->setPauseOnHover(false);
    QVERIFY(!m_autoCarousel->pauseOnHover());

    m_autoCarousel->startAutoPlay();
    
    // Hover should not affect auto-play when disabled
    QEvent hoverEnter(QEvent::HoverEnter);
    QApplication::sendEvent(m_autoCarousel, &hoverEnter);
    QVERIFY(m_autoCarousel->isAutoPlayActive());

    m_autoCarousel->stopAutoPlay();
}

// FluentIndicatorCarousel tests
void FluentCarouselVariantsTest::testIndicatorCarouselConstructor() {
    // Test default constructor
    auto* indicatorCarousel1 = new FluentIndicatorCarousel();
    QVERIFY(indicatorCarousel1 != nullptr);
    QCOMPARE(indicatorCarousel1->indicatorStyle(), FluentCarouselIndicatorStyle::Dots);
    QCOMPARE(indicatorCarousel1->indicatorPosition(), FluentCarouselIndicatorPosition::Bottom);
    delete indicatorCarousel1;

    // Test constructor with configuration
    FluentCarouselConfig config;
    config.indicatorStyle = FluentCarouselIndicatorStyle::Lines;
    config.indicatorPosition = FluentCarouselIndicatorPosition::Top;
    
    auto* indicatorCarousel2 = new FluentIndicatorCarousel(config);
    QCOMPARE(indicatorCarousel2->indicatorStyle(), FluentCarouselIndicatorStyle::Lines);
    QCOMPARE(indicatorCarousel2->indicatorPosition(), FluentCarouselIndicatorPosition::Top);
    delete indicatorCarousel2;
}

void FluentCarouselVariantsTest::testIndicatorCarouselDots() {
    addTestItems(m_indicatorCarousel, 4);

    // Test dot indicators
    m_indicatorCarousel->setIndicatorStyle(FluentCarouselIndicatorStyle::Dots);
    QCOMPARE(m_indicatorCarousel->indicatorStyle(), FluentCarouselIndicatorStyle::Dots);

    // Test indicator count matches item count
    // Would need access to internal indicator widgets to verify count

    // Test indicator updates on navigation
    QSignalSpy indicatorChangedSpy(m_indicatorCarousel, &FluentIndicatorCarousel::indicatorChanged);
    
    m_indicatorCarousel->next();
    QCOMPARE(m_indicatorCarousel->currentIndex(), 1);
    QCOMPARE(indicatorChangedSpy.count(), 1);
}

// Helper methods
void FluentCarouselVariantsTest::addTestItems(FluentCarousel* carousel, int count) {
    for (int i = 0; i < count; ++i) {
        auto* widget = createTestWidget(QString("Item %1").arg(i + 1), 
                                      QColor::fromHsv(i * 60, 255, 255));
        carousel->addItem(widget);
    }
}

QWidget* FluentCarouselVariantsTest::createTestWidget(const QString& text, const QColor& color) {
    auto* widget = new QWidget();
    widget->setFixedSize(200, 150);
    widget->setStyleSheet(QString("background-color: %1; color: white;").arg(color.name()));
    
    auto* label = new QLabel(text, widget);
    label->setAlignment(Qt::AlignCenter);
    label->setGeometry(widget->rect());
    
    return widget;
}

QPixmap FluentCarouselVariantsTest::createTestPixmap(const QSize& size, const QColor& color) {
    QPixmap pixmap(size);
    pixmap.fill(color);
    return pixmap;
}

QTEST_MAIN(FluentCarouselVariantsTest)
#include "FluentCarouselVariantsTest.moc"
