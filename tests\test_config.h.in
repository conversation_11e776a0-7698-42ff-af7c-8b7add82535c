// tests/test_config.h.in
#ifndef FLUENT_QT_TEST_CONFIG_H
#define FLUENT_QT_TEST_CONFIG_H

// Test configuration generated by CMake

// Project information
#define FLUENT_QT_TEST_VERSION_MAJOR @PROJECT_VERSION_MAJOR@
#define FLUENT_QT_TEST_VERSION_MINOR @PROJECT_VERSION_MINOR@
#define FLUENT_QT_TEST_VERSION_PATCH @PROJECT_VERSION_PATCH@
#define FLUENT_QT_TEST_VERSION "@PROJECT_VERSION@"

// Build configuration
#define FLUENT_QT_TEST_BUILD_TYPE "@CMAKE_BUILD_TYPE@"
#define FLUENT_QT_TEST_COMPILER "@CMAKE_CXX_COMPILER_ID@"
#define FLUENT_QT_TEST_COMPILER_VERSION "@CMAKE_CXX_COMPILER_VERSION@"

// Qt version information
#define FLUENT_QT_TEST_QT_VERSION "@Qt6_VERSION@"

// Test directories
#define FLUENT_QT_TEST_SOURCE_DIR "@CMAKE_CURRENT_SOURCE_DIR@"
#define FLUENT_QT_TEST_BINARY_DIR "@CMAKE_CURRENT_BINARY_DIR@"
#define FLUENT_QT_TEST_DATA_DIR "@CMAKE_CURRENT_BINARY_DIR@/data"

// Test features
#cmakedefine FLUENT_QT_TEST_COVERAGE_ENABLED
#cmakedefine FLUENT_QT_TEST_VALGRIND_ENABLED
#cmakedefine FLUENT_QT_TEST_BENCHMARK_ENABLED

// Platform detection
#ifdef _WIN32
    #define FLUENT_QT_TEST_PLATFORM_WINDOWS
#elif defined(__APPLE__)
    #define FLUENT_QT_TEST_PLATFORM_MACOS
#elif defined(__linux__)
    #define FLUENT_QT_TEST_PLATFORM_LINUX
#else
    #define FLUENT_QT_TEST_PLATFORM_UNKNOWN
#endif

// Test utilities
#define FLUENT_QT_TEST_TIMEOUT_MS 30000
#define FLUENT_QT_TEST_WAIT_MS 100

// Test data paths
#define FLUENT_QT_TEST_ICONS_DIR FLUENT_QT_TEST_DATA_DIR "/icons"
#define FLUENT_QT_TEST_THEMES_DIR FLUENT_QT_TEST_DATA_DIR "/themes"
#define FLUENT_QT_TEST_FONTS_DIR FLUENT_QT_TEST_DATA_DIR "/fonts"

// Test macros
#define FLUENT_QT_TEST_MAIN(TestClass) \
    int main(int argc, char *argv[]) { \
        QApplication app(argc, argv); \
        app.setAttribute(Qt::AA_Use96Dpi, true); \
        TestClass tc; \
        return QTest::qExec(&tc, argc, argv); \
    }

#define FLUENT_QT_TEST_VERIFY_TIMEOUT(condition, timeout) \
    do { \
        int elapsed = 0; \
        while (!(condition) && elapsed < (timeout)) { \
            QTest::qWait(10); \
            elapsed += 10; \
        } \
        QVERIFY2((condition), "Condition not met within timeout"); \
    } while (0)

#define FLUENT_QT_TEST_COMPARE_TIMEOUT(actual, expected, timeout) \
    do { \
        int elapsed = 0; \
        while ((actual) != (expected) && elapsed < (timeout)) { \
            QTest::qWait(10); \
            elapsed += 10; \
        } \
        QCOMPARE((actual), (expected)); \
    } while (0)

// Test helper functions
namespace FluentQtTest {
    
    // Get test data directory
    inline QString getTestDataDir() {
        return QString(FLUENT_QT_TEST_DATA_DIR);
    }
    
    // Get test resource path
    inline QString getTestResource(const QString& relativePath) {
        return getTestDataDir() + "/" + relativePath;
    }
    
    // Wait for condition with timeout
    template<typename Condition>
    bool waitForCondition(Condition condition, int timeoutMs = FLUENT_QT_TEST_TIMEOUT_MS) {
        int elapsed = 0;
        while (!condition() && elapsed < timeoutMs) {
            QTest::qWait(FLUENT_QT_TEST_WAIT_MS);
            elapsed += FLUENT_QT_TEST_WAIT_MS;
        }
        return condition();
    }
    
    // Create test widget with standard setup
    template<typename WidgetType>
    WidgetType* createTestWidget() {
        WidgetType* widget = new WidgetType();
        widget->show();
        QTest::qWaitForWindowExposed(widget);
        return widget;
    }
    
    // Simulate user interaction delay
    inline void simulateUserDelay(int ms = 50) {
        QTest::qWait(ms);
    }
    
    // Check if running in CI environment
    inline bool isRunningInCI() {
        return qEnvironmentVariableIsSet("CI") || 
               qEnvironmentVariableIsSet("GITHUB_ACTIONS") ||
               qEnvironmentVariableIsSet("GITLAB_CI") ||
               qEnvironmentVariableIsSet("JENKINS_URL");
    }
    
    // Get appropriate timeout for CI vs local testing
    inline int getTimeout(int localTimeout = 5000) {
        return isRunningInCI() ? localTimeout * 3 : localTimeout;
    }
    
    // Skip test if feature not available
    inline void skipIfFeatureNotAvailable(const QString& feature) {
        if (!QTest::currentTestFunction()) {
            QSKIP(qPrintable(QString("Feature '%1' not available").arg(feature)));
        }
    }
    
    // Generate test data for parameterized tests
    template<typename T>
    void addTestData(const QString& tag, const T& value) {
        QTest::newRow(qPrintable(tag)) << value;
    }
    
    // Verify widget geometry is reasonable
    inline bool isValidGeometry(const QRect& geometry) {
        return geometry.width() > 0 && 
               geometry.height() > 0 && 
               geometry.width() < 10000 && 
               geometry.height() < 10000;
    }
    
    // Create temporary directory for test files
    inline QString createTempDir(const QString& prefix = "fluent_qt_test") {
        QTemporaryDir tempDir(prefix);
        tempDir.setAutoRemove(false);
        return tempDir.path();
    }
    
    // Clean up test resources
    inline void cleanupTestResources() {
        // Clean up any global test state
        QApplication::processEvents();
    }
}

// Test categories for filtering
#define FLUENT_QT_TEST_CATEGORY_UNIT "unit"
#define FLUENT_QT_TEST_CATEGORY_INTEGRATION "integration"
#define FLUENT_QT_TEST_CATEGORY_ACCESSIBILITY "accessibility"
#define FLUENT_QT_TEST_CATEGORY_PERFORMANCE "performance"
#define FLUENT_QT_TEST_CATEGORY_VISUAL "visual"

// Test severity levels
#define FLUENT_QT_TEST_SEVERITY_CRITICAL "critical"
#define FLUENT_QT_TEST_SEVERITY_HIGH "high"
#define FLUENT_QT_TEST_SEVERITY_MEDIUM "medium"
#define FLUENT_QT_TEST_SEVERITY_LOW "low"

#endif // FLUENT_QT_TEST_CONFIG_H
